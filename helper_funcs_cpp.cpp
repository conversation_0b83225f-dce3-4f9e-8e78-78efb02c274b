void SymmetryDetector::symm_inversion(int natoms, const std::vector<int>& nat,
                                     const std::vector<std::vector<double>>& coord, double delta,
                                     int& nc, std::vector<int>& ntrans, double& delta3) {
    // Input validation
    if (natoms <= 0) {
        throw std::runtime_error("symm_inversion: natoms must be positive");
    }
    if (nat.size() < static_cast<size_t>(natoms)) {
        throw std::runtime_error("symm_inversion: nat array too small");
    }
    if (coord.size() != 3 ||
        std::any_of(coord.begin(), coord.end(), [natoms](const auto& c) { return c.size() != static_cast<size_t>(natoms); })) {
        throw std::runtime_error("symm_inversion: coord must be [3][natoms]");
    }

    // Create inverted coordinates in 3xN format as expected by symm_check
    std::vector<std::vector<double>> cord(3, std::vector<double>(natoms));
    for (int i = 0; i < natoms; ++i) {
        cord[0][i] = -coord[0][i];
        cord[1][i] = -coord[1][i];
        cord[2][i] = -coord[2][i];
    }

    // Check if inversion is a symmetry operation
    symm_check(natoms, delta, nat, coord, cord, nc, ntrans, delta3);

    // If not a symmetry operation, set identity permutation
    if (nc != natoms) {
        ntrans.resize(natoms);
        for (int i = 0; i < natoms; ++i) {
            ntrans[i] = i; // 0-based identity permutation
        }
    }
}


/**
 * @brief Performs a rotation around axis v1 and checks if it's a symmetry operation
 * @param natoms Number of atoms
 * @param nat Atomic numbers
 * @param coord Coordinates (3*natoms)
 * @param v1 Rotation axis vector
 * @param sina Sine of rotation angle
 * @param cosa Cosine of rotation angle
 * @param delta Tolerance for coordinate comparison
 * @param nc [out] Number of unchanged atoms
 * @param ntrans [out] Permutation array
 * @param delta3 [out] Maximum deviation
 */
void SymmetryDetector::symm_rotate(int natoms, const std::vector<int>& nat,
                                  const std::vector<std::vector<double>>& coord, const Vector3D& v1,
                                  double sina, double cosa, double delta,
                                  int& nc, std::vector<int>& ntrans, double& delta3) {
    // Input validation
    if (natoms <= 0) {
        throw std::runtime_error("symm_rotate: natoms must be positive");
    }
    if (nat.size() < static_cast<size_t>(natoms)) {
        throw std::runtime_error("symm_rotate: nat array too small");
    }
    if (coord.size() != 3 ||
        std::any_of(coord.begin(), coord.end(), [natoms](const auto& c) { return c.size() != static_cast<size_t>(natoms); })) {
        throw std::runtime_error("symm_rotate: coord must be [3][natoms]");
    }
    // Optional: check if v1 is normalized
    double norm = std::sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
    if (std::abs(norm - 1.0) > 1e-10) {
        throw std::runtime_error("symm_rotate: rotation axis must be normalized");
    }

    // Create rotated coordinates
    std::vector<std::vector<double>> cord(3, std::vector<double>(natoms));
    for (int j = 0; j < natoms; ++j) {
        // Original position
        Vector3D p0 = {coord[0][j], coord[1][j], coord[2][j]};

        // Compute rotation using Rodrigues' formula: p = p0 + sina*(v1 × p0) + (1-cosa)*(v1 × (v1 × p0))
        Vector3D v2 = symm_crossp(v1, p0);
        Vector3D v3 = symm_crossp(v1, v2);
        Vector3D p = {
            p0.x + sina * v2.x + (1.0 - cosa) * v3.x,
            p0.y + sina * v2.y + (1.0 - cosa) * v3.y,
            p0.z + sina * v2.z + (1.0 - cosa) * v3.z
        };

        // Store rotated coordinates
        cord[0][j] = p.x;
        cord[1][j] = p.y;
        cord[2][j] = p.z;
    }

    // Check if rotation is a symmetry operation
    symm_check(natoms, delta, nat, coord, cord, nc, ntrans, delta3);
}



/**
 * @brief Performs a reflection across a plane and checks if it's a symmetry operation
 * @param natoms Number of atoms
 * @param nat Atomic numbers
 * @param coord Coordinates (3*natoms)
 * @param v Normal vector of the reflection plane
 * @param p0 A point on the reflection plane
 * @param delta Tolerance for coordinate comparison
 * @param nc [out] Number of unchanged atoms
 * @param ntrans [out] Permutation array
 * @param delta3 [out] Maximum deviation
 */
void SymmetryDetector::symm_reflect(int natoms, const std::vector<int>& nat,
                                     const std::vector<std::vector<double>>& coord, const Vector3D& v,
                                     const Vector3D& p0, double delta,
                                     int& nc, std::vector<int>& ntrans, double& delta3) {
    // Input validation
    if (natoms <= 0) {
        throw std::runtime_error("symm_reflect: natoms must be positive");
    }
    if (nat.size() < static_cast<size_t>(natoms)) {
        throw std::runtime_error("symm_reflect: nat array too small");
    }
    if (coord.size() < 3 || coord[0].size() < static_cast<size_t>(natoms)) {
        throw std::runtime_error("symm_reflect: coord array too small");
    }
    // Check if v is normalized
    double norm = std::sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    if (std::abs(norm - 1.0) > 1e-10) {
        throw std::runtime_error("symm_reflect: normal vector must be normalized");
    }

    // Create reflected coordinates
    std::vector<std::vector<double>> cord(3, std::vector<double>(natoms));
    for (int i = 0; i < natoms; ++i) {
        // Current atom's position
        Vector3D p = {coord[0][i], coord[1][i], coord[2][i]};

        // Compute vk = -dot(v, p - p0)
        double p_minus_p0[3] = {p.x - p0.x, p.y - p0.y, p.z - p0.z};
        double vk = -symm_dot(v.data(), p_minus_p0, 3);

        // Apply reflection: coord + 2*vk*v
        cord[0][i] = coord[0][i] + 2.0 * vk * v.x;
        cord[1][i] = coord[1][i] + 2.0 * vk * v.y;
        cord[2][i] = coord[2][i] + 2.0 * vk * v.z;
    }

    // Check if reflection is a symmetry operation
    symm_check(natoms, delta, nat, coord, cord, nc, ntrans, delta3);
}



/**
 * @brief Performs an improper rotation (rotation + reflection) around axis v1
 * @param natoms Number of atoms
 * @param nat Atomic numbers
 * @param coord Coordinates (3*natoms)
 * @param v1 Rotation axis vector
 * @param sina Sine of rotation angle
 * @param cosa Cosine of rotation angle
 * @param delta Tolerance for coordinate comparison
 * @param nc [out] Number of unchanged atoms
 * @param ntrans [out] Permutation array
 * @param delta3 [out] Maximum deviation
 */
void SymmetryDetector::symm_srotate(int natoms, const std::vector<int>& nat,
                                     const std::vector<std::vector<double>>& coord, const Vector3D& v1,
                                     double sina, double cosa, double delta,
                                     int& nc, std::vector<int>& ntrans, double& delta3) {
    // Input validation
    if (natoms <= 0) {
        throw std::runtime_error("symm_srotate: natoms must be positive");
    }
    if (nat.size() < static_cast<size_t>(natoms)) {
        throw std::runtime_error("symm_srotate: nat array too small");
    }
    if (coord.size() < 3 || coord[0].size() < static_cast<size_t>(natoms)) {
        throw std::runtime_error("symm_srotate: coord array too small");
    }
    // Check if v1 is normalized
    double norm = std::sqrt(v1.x * v1.x + v1.y * v1.y + v1.z * v1.z);
    if (std::abs(norm - 1.0) > 1e-10) {
        throw std::runtime_error("symm_srotate: rotation axis must be normalized");
    }

    // Step 1: Rotate coordinates
    std::vector<std::vector<double>> cord(3, std::vector<double>(natoms));
    for (int l = 0; l < natoms; ++l) {
        // Original position
        Vector3D p0 = {coord[l][0], coord[l][1], coord[l][2]};

        // Rotate using Rodrigues' formula: p = p0 + sina*(v1 × p0) + (1-cosa)*(v1 × (v1 × p0))
        Vector3D v2 = symm_crossp(v1, p0);
        Vector3D v3 = symm_crossp(v1, v2);
        Vector3D p = {
            p0.x + sina * v2.x + (1.0 - cosa) * v3.x,
            p0.y + sina * v2.y + (1.0 - cosa) * v3.y,
            p0.z + sina * v2.z + (1.0 - cosa) * v3.z
        };

        // Store rotated coordinates
        cord[0][l] = p.x;
        cord[1][l] = p.y;
        cord[2][l] = p.z;
    }

    // Step 2: Reflect across plane through origin with normal v1
    std::vector<std::vector<double>> cc(natoms, std::vector<double>(3));
    for (int j = 0; j < natoms; ++j) {
        // Rotated position
        Vector3D p0 = {cord[j][0], cord[j][1], cord[j][2]};

        // Compute vk = -dot(v1, p0) (plane through origin)
        double vk = -symm_dot(v1.data(), p0.data(), 3);

        // Apply reflection: cc = cord + 2*vk*v1
        cc[j][0] = cord[j][0] + 2.0 * vk * v1.x;
        cc[j][1] = cord[j][1] + 2.0 * vk * v1.y;
        cc[j][2] = cord[j][2] + 2.0 * vk * v1.z;
    }

    // Check if improper rotation is a symmetry operation
    symm_check(natoms, delta, nat, coord, cc, nc, ntrans, delta3);
}


// Inline functions
inline double symm_dot(const double* v1, const double* v2, size_t n) {
    double result = 0.0;
    for (size_t i = 0; i < n; ++i) {
        result += v1[i] * v2[i];
    }
    return result;
}

inline double symm_dot(const std::vector<double>& v1, const std::vector<double>& v2) {
    if (v1.size() != v2.size()) {
        throw std::invalid_argument("Vectors must have the same size");
    }
    double result = 0.0;
    for (size_t i = 0; i < v1.size(); ++i) {
        result += v1[i] * v2[i];
    }
    return result;
}

inline double symm_dot(const std::array<double, 3>& v1, const std::array<double, 3>& v2) {
    double result = 0.0;
    for (size_t i = 0; i < 3; ++i) {
        result += v1[i] * v2[i];
    }
    return result;
}

inline Vector3D symm_crossp(const Vector3D& x, const Vector3D& y) {
    Vector3D z;
    z.x = x.y * y.z - x.z * y.y;
    z.y = -(x.x * y.z) + x.z * y.x;
    z.z = x.x * y.y - x.y * y.x;
    return z;
}

inline std::vector<double> symm_crossp(const std::vector<double>& x, const std::vector<double>& y) {
    if (x.size() != 3 || y.size() != 3) {
        throw std::invalid_argument("Vectors must have size 3");
    }
    std::vector<double> z(3);
    z[0] = x[1] * y[2] - x[2] * y[1];
    z[1] = -(x[0] * y[2]) + x[2] * y[0];
    z[2] = x[0] * y[1] - x[1] * y[0];
    return z;
}

inline void symm_crossp(const std::vector<double>& x, const std::vector<double>& y, std::vector<double>& z) {
    if (x.size() != 3 || y.size() != 3) {
        throw std::invalid_argument("Vectors must have size 3");
    }
    z.resize(3);
    z[0] = x[1] * y[2] - x[2] * y[1];
    z[1] = -(x[0] * y[2]) + x[2] * y[0];
    z[2] = x[0] * y[1] - x[1] * y[0];
}

inline void symm_crossp(const std::array<double, 3>& x, const std::array<double, 3>& y, std::array<double, 3>& z) {
    z[0] = x[1] * y[2] - x[2] * y[1];
    z[1] = -(x[0] * y[2]) + x[2] * y[0];
    z[2] = x[0] * y[1] - x[1] * y[0];
}