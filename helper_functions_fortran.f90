************************************************************************
      double precision function syva_dot(x,y,n)                         
************************************************************************
c
c Scalar (dot or inner) product of two vectors: <x|y>=x.y
c
      implicit double precision(a-h,o-z)                               
      implicit integer(i-n)
      dimension x(*),y(*)                                         
      syva_dot=0.d0                                                          
      do i=1,n                                                          
         syva_dot=syva_dot+x(i)*y(i)                                              
      end do                                                            
      return                                                            
      end                                                               
************************************************************************
      subroutine syva_crossp(x,y,z)
************************************************************************
c
c Vector (cross or outer) product of two vectors: z=[x,y] 
c
      implicit double precision(a-h,o-z)
      implicit integer(i-n)
      dimension x(3),y(3),z(3)
      z(1)=x(2)*y(3)-x(3)*y(2)
      z(2)=-x(1)*y(3)+x(3)*y(1)
      z(3)=x(1)*y(2)-x(2)*y(1)
      return
      end

************************************************************************
      subroutine syva_inversion(natoms,nat,coord,delta,nc,ntrans,delta3)
************************************************************************
c
c Performs an inversion to the origin
c
      implicit double precision(a-h,o-z)
      implicit integer(i-n)
      dimension nat(natoms),coord(3,natoms)
      dimension cord(3,natoms),ntrans(natoms)
      do i=1,3
         do j=1,natoms
            cord(i,j)=-coord(i,j)
         end do
      end do
      call syva_check(natoms,delta,nat,coord,cord,nc,ntrans,delta3)
      if(nc.ne.natoms) then
         do i=1,natoms
            ntrans(i)=i
         end do
      end if
      return
      end
************************************************************************
      subroutine syva_rotate(natoms,nat,coord,v1,sina,cosa,delta,
     &   nc,ntrans,delta3)
************************************************************************
c
c Performs a rotation around axis v1
c
      implicit double precision(a-h,o-z)
      implicit integer(i-n)
      dimension nat(natoms),coord(3,natoms)
      dimension cord(3,natoms),ntrans(natoms)
      dimension v1(3),v2(3),v3(3),p0(3),p(3)
      do j=1,natoms
         p0(1)=coord(1,j)
         p0(2)=coord(2,j)
         p0(3)=coord(3,j)
         call syva_crossp(v1,p0,v2)
         call syva_crossp(v1,v2,v3)
         p=p0+sina*v2+(1.d0-cosa)*v3
         cord(1,j)=p(1)
         cord(2,j)=p(2)
         cord(3,j)=p(3)
      end do
      call syva_check(natoms,delta,nat,coord,cord,nc,ntrans,delta3)
      return
      end
************************************************************************
      subroutine syva_reflect(natoms,nat,coord,v,p0,delta,nc,
     & ntrans,delta3)
************************************************************************
c
c Performs a reflection to the plane v
c
      implicit double precision(a-h,o-z)
      implicit integer(i-n)
      dimension nat(natoms),coord(3,natoms)
      dimension cord(3,natoms),ntrans(natoms)
      dimension v(3),p0(3),p(3)
      do i=1,natoms
         p(1)=coord(1,i)
         p(2)=coord(2,i)
         p(3)=coord(3,i)
         vk=-syva_dot(v,p-p0,3)
         cord(1,i)=coord(1,i)+2.d0*vk*v(1)
         cord(2,i)=coord(2,i)+2.d0*vk*v(2)
         cord(3,i)=coord(3,i)+2.d0*vk*v(3)
      end do
      call syva_check(natoms,delta,nat,coord,cord,nc,ntrans,delta3)
      return
      end
************************************************************************
      subroutine syva_srotate(natoms,nat,coord,v1,sina,cosa,delta,
     &   nc,ntrans,delta3)
************************************************************************
c
c Improper rotation around axis v1
c
      implicit double precision(a-h,o-z)
      implicit integer(i-n)
      dimension nat(natoms),coord(3,natoms)
      dimension cord(3,natoms),cc(3,natoms),ntrans(natoms)
      dimension v1(3),v2(3),v3(3),p0(3),p(3)
      do l=1,natoms
         p0(1)=coord(1,l)
         p0(2)=coord(2,l)
         p0(3)=coord(3,l)
         call syva_crossp(v1,p0,v2)
         call syva_crossp(v1,v2,v3)
         p=p0+sina*v2+(1.d0-cosa)*v3
         cord(1,l)=p(1)
         cord(2,l)=p(2)
         cord(3,l)=p(3)
      end do
      do j=1,natoms
         p0(1)=cord(1,j)
         p0(2)=cord(2,j)
         p0(3)=cord(3,j)
         vk=-syva_dot(v1,p0,3)
         cc(1,j)=cord(1,j)+2.d0*vk*v1(1)
         cc(2,j)=cord(2,j)+2.d0*vk*v1(2)
         cc(3,j)=cord(3,j)+2.d0*vk*v1(3)
      end do
      call syva_check(natoms,delta,nat,coord,cc,nc,ntrans,delta3)
      return
      end






