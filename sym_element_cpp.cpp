void SymmetryDetector::sym_elements(int natoms, const std::vector<int>& nat,
                                    const std::vector<std::vector<double>>& coord,
                                    const std::vector<std::string>& symb, double delta,
                                    int nout, int& norder, int& ni, int& nsg, int& ncr,
                                    int& nsr, int& np, std::vector<std::vector<double>>& symn,
                                    std::vector<std::vector<int>>& nsym, int& nprm,
                                    std::vector<std::vector<int>>& nper, int& nseq,
                                    std::vector<int>& nccl, std::vector<std::vector<int>>& nscl) {
    const int nmax = 150;
    // Input validation
    if (natoms <= 0) throw std::runtime_error("sym_elements: natoms must be positive");
    if (nat.size() < static_cast<size_t>(natoms)) throw std::runtime_error("sym_elements: nat array too small");
    // Coordinate validation will be done after format detection
    // symb parameter is optional - if empty, we'll work without symbol information
    if (!symb.empty() && symb.size() < 90) throw std::runtime_error("sym_elements: symb array too small");
    if (delta < 0.0) throw std::runtime_error("sym_elements: delta must be non-negative");

    // Check coordinate format
    // Assume Nx3 format (atoms x coordinates) unless clearly 3xN
    bool is_nx3 = false;

    if (coord.size() == static_cast<size_t>(natoms) && !coord.empty() && coord[0].size() == 3) {
        // Nx3 format: coord[natoms][3]
        is_nx3 = true;
    } else if (coord.size() == 3 && !coord.empty() && coord[0].size() == static_cast<size_t>(natoms)) {
        // 3xN format: coord[3][natoms]
        // Note: 3xN format detected but not used in current implementation
    } else {
        std::string error_msg = "sym_elements: coord array format not recognized. ";
        error_msg += "Size: " + std::to_string(coord.size());
        if (!coord.empty()) {
            error_msg += " x " + std::to_string(coord[0].size());
        }
        error_msg += ", expected either [" + std::to_string(natoms) + "][3] or [3][" + std::to_string(natoms) + "]";
        throw std::runtime_error(error_msg);
    }

    // Create coordinate array in 3xN format for consistent access
    std::vector<std::vector<double>> coord_3xn;

    if (is_nx3) {
        // Convert Nx3 to 3xN
        coord_3xn.resize(3, std::vector<double>(natoms));
        for (int i = 0; i < natoms; ++i) {
            coord_3xn[0][i] = coord[i][0]; // x
            coord_3xn[1][i] = coord[i][1]; // y
            coord_3xn[2][i] = coord[i][2]; // z
        }
    } else {
        // Already 3xN format or copy as-is
        coord_3xn = coord;
    }

    // Initialize outputs
    double delta3 = 0.0;
    nprm = 1;
    nper.assign(natoms, std::vector<int>(250, 0));
    for (int i = 0; i < natoms; ++i) {
        nper[i][0] = i + 1; // Identity permutation (1-based)
    }
    symn.assign(3, std::vector<double>(nmax, 0.0));
    nsym.assign(nmax, std::vector<int>(5, 0));
    nseq = 0;
    nccl.assign(natoms, 0);
    nscl.assign(natoms, std::vector<int>(natoms, 0));

    // Compute PI
    const double pi = 4.0 * std::atan(1.0);

    // Partition atoms into equivalence classes based on atomic numbers
    std::vector<int> meq(natoms, 0);
    std::vector<std::vector<int>> ieq(natoms, std::vector<int>(natoms, 0));
    int neq = 1;
    meq[0] = 1;
    ieq[0][0] = 1;
    for (int i = 1; i < natoms; ++i) {
        int nati = nat[i];
        bool found = false;
        for (int j = 0; j < neq; ++j) {
            int natj = nat[ieq[j][0] - 1];
            if (nati == natj) {
                meq[j]++;
                ieq[j][meq[j] - 1] = i + 1;
                found = true;
                break;
            }
        }
        if (!found) {
            meq[neq] = 1;
            ieq[neq][0] = i + 1;
            neq++;
        }
    }
    if (nout == 2) {
        std::cout << "\n-- Equivalence classes of atoms: " << neq << "\n";
        for (int i = 0; i < neq; ++i) {
            std::string atom_symbol = (!symb.empty() && nat[ieq[i][0] - 1] - 1 < static_cast<int>(symb.size()))
                ? symb[nat[ieq[i][0] - 1] - 1] : std::to_string(nat[ieq[i][0] - 1]);
            std::cout << "\n    #" << i + 1 << " (atom " << atom_symbol << ")\n";
            std::cout << "    ";
            for (int j = 0; j < meq[i]; ++j) {
                std::cout << std::setw(4) << ieq[i][j];
            }
            std::cout << "\n";
        }
    }

    // Initialize symmetry flags and counters
    bool symcen = false, linear = false, planar = false;
    nsg = 0;
    int nrot = 0;
    std::vector<std::vector<double>> sigman(nmax, std::vector<double>(3, 0.0));
    std::vector<std::vector<double>> rotn(nmax, std::vector<double>(3, 0.0));
    std::vector<double> rota(nmax, 0.0);
    std::vector<int> ntrans(natoms, 0);
    std::vector<double> p0(3), p1(3), p2(3), p3(3), v1(3), v2(3), v3(3), v0(3), a(3), b(3), c(3);

    // Check for inversion center
    double del;
    int nc;
    symm_inversion(natoms, nat, coord, delta, nc, ntrans, del);
    if (nc == natoms) {
        symcen = true;
        if (del > delta3) delta3 = del;
        nprm = 2;
        for (int i = 0; i < natoms; ++i) {
            nper[i][1] = ntrans[i]; // 1-based
        }
    }
    int icent = 0;
    if (symcen) {
        if (nout >= 1) std::cout << "\n-- CENTRE OF SYMMETRY: {i}\n";
        nsym[0][1] = 1;
    }
    for (int i = 0; i < natoms; ++i) {
        p0 = {coord[0][i], coord[1][i], coord[2][i]};
        double sp = std::sqrt(symm_dot(p0.data(), p0.data(), 3));
        if (sp <= delta) {
            icent = i + 1;
            if (sp > delta3) delta3 = sp;
        }
    }
    if (icent > 0 && nout == 2) {
        std::string atom_symbol = (!symb.empty() && nat[icent - 1] - 1 < static_cast<int>(symb.size()))
            ? symb[nat[icent - 1] - 1] : std::to_string(nat[icent - 1]);
        std::cout << "\n-- Atom " << atom_symbol << " (" << icent << ") in the COM\n";
    }
    nsym[0][2] = icent;
    if (icent > 0) nsym[0][4] = 1;

    // Check for linear molecule
    bool found_non_linear = false;
    for (int i = 0; i < natoms - 1 && !found_non_linear; ++i) {
        if (i + 1 == icent) continue;
        p1[0] = coord_3xn[0][i];
        p1[1] = coord_3xn[1][i];
        p1[2] = coord_3xn[2][i];
        for (int j = i + 1; j < natoms && !found_non_linear; ++j) {
            if (j + 1 == icent) continue;
            p2[0] = coord_3xn[0][j];
            p2[1] = coord_3xn[1][j];
            p2[2] = coord_3xn[2][j];
            symm_crossp(p1, p2, p0);
            double vn = std::sqrt(symm_dot(p0.data(), p0.data(), 3));
            if (vn > delta) {
                linear = false;
                found_non_linear = true;
            }
            if (vn > delta3) delta3 = vn;
        }
    }
    if (!found_non_linear) {
        linear = true;
    }
    if (nout >= 1) std::cout << "\n-- LINEAR MOLECULE\n";
    if (symcen) {
        if (nout >= 1) std::cout << "\n-- The structure should belong to the Dinf_h point group.\n";
        if (nout >= 1) std::cout << "\n-- PLANES OF SYMMETRY --\n";
        nsg = 1;
        nsym[1][0] = 1;
        nsym[1][3] = 2;
        nsym[1][4] = natoms;
        if (nout >= 1) std::cout << "\n-- Infinite planes\n";
        if (nout == 2) std::cout << "    All atoms included.\n";
        if (nout >= 1) std::cout << "\n-- Distinct PROPER ROTATIONAL AXES --\n";
        ncr = 2;
        nsym[2][0] = 2;
        nsym[2][2] = 1;
        nsym[2][3] = 1;
        nsym[2][4] = natoms;
        symn[0][2] = icent != 1 ? coord[0][0] : coord[0][1];
        symn[1][2] = icent != 1 ? coord[1][0] : coord[1][1];
        symn[2][2] = icent != 1 ? coord[2][0] : coord[2][1];
        double norm = std::sqrt(symm_dot(&symn[0][2], &symn[0][2], 3));
        for (int k = 0; k < 3; ++k) symn[k][2] /= norm;
        nsym[3][0] = 2;
        nsym[3][1] = 2;
        nsym[3][2] = 1;
        nsym[3][3] = 2;
        if (nout >= 1) std::cout << "\n-- Axis #1: C(0.00)\n";
        if (nout == 2) {
            std::cout << "  d: " << std::fixed << std::setprecision(5)
                      << std::setw(12) << symn[0][2] << std::setw(12) << symn[1][2] << std::setw(12) << symn[2][2] << "\n";
            std::cout << "    All atoms included.\n";
        }
        if (nout >= 1) std::cout << "\n-- Axis #2: C(180.00)\n";
        if (nout == 2) {
            std::cout << "  d: " << std::fixed << std::setprecision(5)
                      << std::setw(12) << symn[0][3] << std::setw(12) << symn[1][3] << std::setw(12) << symn[2][3] << "\n";
            std::cout << "    Atoms included:\n";
            if (icent > 0) {
                std::string atom_symbol = (!symb.empty() && nat[nsym[0][2] - 1] - 1 < static_cast<int>(symb.size()))
                    ? symb[nat[nsym[0][2] - 1] - 1] : std::to_string(nat[nsym[0][2] - 1]);
                std::cout << "        " << atom_symbol << " (" << nsym[0][2] << ")\n";
            }
        }
        nsr = 1;
        nsym[4][0] = 3;
        nsym[4][2] = 1;
        nsym[4][4] = 1;
        symn[0][4] = icent != 1 ? coord_3xn[0][0] : coord_3xn[0][1];
        symn[1][4] = icent != 1 ? coord_3xn[1][0] : coord_3xn[1][1];
        symn[2][4] = icent != 1 ? coord_3xn[2][0] : coord_3xn[2][1];
        norm = std::sqrt(symm_dot(&symn[0][4], &symn[0][4], 3));
        for (int k = 0; k < 3; ++k) symn[k][4] /= norm;
        ni = 1;
        norder = -1;
        np = -1;
        if (nout >= 1) std::cout << "\n-- Number of symmetry operations = infinite\n";
    } else {
        if (nout >= 1) std::cout << "\n-- The structure should belong to the Cinf_v point group.\n";
        if (nout >= 1) std::cout << "\n-- PLANES OF SYMMETRY --\n";
        nsg = 1;
        nsym[1][0] = 1;
        nsym[1][3] = 2;
        nsym[1][4] = natoms;
        if (nout >= 1) std::cout << "\n-- Infinite planes\n";
        if (nout == 2) std::cout << "    All atoms included.\n";
        if (nout >= 1) std::cout << "\n-- Distinct PROPER ROTATIONAL AXES --\n";
        ncr = 1;
        nsym[2][0] = 2;
        nsym[2][2] = 1;
        nsym[2][3] = 1;
        nsym[2][4] = natoms;
        symn[0][2] = icent != 1 ? coord[0][0] : coord[0][1];
        symn[1][2] = icent != 1 ? coord[1][0] : coord[1][1];
        symn[2][2] = icent != 1 ? coord[2][0] : coord[2][1];
        double norm = std::sqrt(symm_dot(&symn[0][2], &symn[0][2], 3));
        for (int k = 0; k < 3; ++k) symn[k][2] /= norm;
        if (nout >= 1) std::cout << "\n-- Axis #1: C(0.00)\n";
        if (nout == 2) {
            std::cout << "  d: " << std::fixed << std::setprecision(5)
                      << std::setw(12) << symn[0][2] << std::setw(12) << symn[1][2] << std::setw(12) << symn[2][2] << "\n";
            std::cout << "    All atoms included.\n";
        }
        nsr = 0;
        norder = -1;
        ni = 0;
        np = -1;
        if (nout >= 1) std::cout << "\n-- Number of symmetry operations = infinite\n";
    }
    if (!linear) {
        // Planar check
        v1 = p0;
        double vn = std::sqrt(symm_dot(v1.data(), v1.data(), 3));
        v1 = {v1[0] / vn, v1[1] / vn, v1[2] / vn};
        bool is_planar = true;
        for (int i = 0; i < natoms && is_planar; ++i) {
            p3 = {coord[0][i], coord[1][i], coord[2][i]};
            double sp = symm_dot(v1.data(), p3.data(), 3);
            if (std::abs(sp) > delta) {
                is_planar = false;
            } else if (std::abs(sp) > delta3) {
                delta3 = std::abs(sp);
            }
        }

        if (is_planar) {
            planar = true;
            nsg++;
            sigman[nsg - 1] = v1;
            if (nout >= 1) std::cout << "\n-- PLANAR MOLECULE\n";
            if (nout == 2) {
                std::cout << "  n: " << std::fixed << std::setprecision(5)
                          << std::setw(12) << v1[0] << std::setw(12) << v1[1] << std::setw(12) << v1[2] << "\n";
            }
            if (symcen && planar) {
                for (int i = 12; i >= 2; --i) {
                    double alpha = 2.0 * pi / i;
                    double sp = alpha * 180.0 / pi;
                    double sina = std::sin(alpha);
                    double cosa = std::cos(alpha);
                    std::cout << "DEBUG: coord_3xn.size() = " << coord_3xn.size();
                    if (!coord_3xn.empty()) std::cout << ", coord_3xn[0].size() = " << coord_3xn[0].size();
                    std::cout << std::endl;
                    symm_rotate(natoms, nat, coord_3xn, Vector3D(v1[0], v1[1], v1[2]), sina, cosa, delta, nc, ntrans, del);
                    if (nc == natoms) {
                        std::vector<std::array<double, 3>> rotn_arr(nrot + 1);
                        std::vector<double> rota_arr(nrot + 1);
                        for (int k = 0; k < nrot; ++k) {
                            rotn_arr[k] = {rotn[k][0], rotn[k][1], rotn[k][2]};
                            rota_arr[k] = rota[k];
                        }
                        add_Cn(nrot, rotn_arr, rota_arr, {v1[0], v1[1], v1[2]}, {p3[0], p3[1], p3[2]}, sp, delta);
                        for (int k = 0; k < nrot; ++k) {
                            rotn[k] = {rotn_arr[k][0], rotn_arr[k][1], rotn_arr[k][2]};
                            rota[k] = rota_arr[k];
                        }
                        add_perm(natoms, ntrans, nprm, nper);
                        if (del > delta3) delta3 = del;
                    }
                }
            }
        }

        // Mirror planes
        if (nout >= 1) std::cout << "\n-- PLANES OF SYMMETRY --\n";
        for (int i = 0; i < neq; ++i) {
            int meqi = meq[i];
            if (meqi == 1) continue;
            for (int j1 = 0; j1 < meqi - 1; ++j1) {
                int i1 = ieq[i][j1] - 1;
                p1 = {coord[0][i1], coord[1][i1], coord[2][i1]};
                for (int j2 = j1 + 1; j2 < meqi; ++j2) {
                    int i2 = ieq[i][j2] - 1;
                    p2 = {coord[0][i2], coord[1][i2], coord[2][i2]};
                    p0 = {(p1[0] + p2[0]) / 2.0, (p1[1] + p2[1]) / 2.0, (p1[2] + p2[2]) / 2.0};
                    v1 = {p2[0] - p0[0], p2[1] - p0[1], p2[2] - p0[2]};
                    vn = std::sqrt(symm_dot(v1.data(), v1.data(), 3));
                    if (vn > delta) {
                        v1 = {v1[0] / vn, v1[1] / vn, v1[2] / vn};
                        double sp = symm_dot(v1.data(), p0.data(), 3);
                        if (std::abs(sp) < delta) {
                            symm_reflect(natoms, nat, coord_3xn, Vector3D(v1[0], v1[1], v1[2]), Vector3D(p0[0], p0[1], p0[2]), delta, nc, ntrans, del);
                            if (nc == natoms) {
                                if (del > delta3) delta3 = del;
                                std::vector<std::array<double, 3>> sigman_arr(nsg + 1);
                                for (int k = 0; k < nsg; ++k) {
                                    sigman_arr[k] = {sigman[k][0], sigman[k][1], sigman[k][2]};
                                }
                                add_SG(nsg, sigman_arr, {v1[0], v1[1], v1[2]}, {p3[0], p3[1], p3[2]}, delta);
                                for (int k = 0; k < nsg; ++k) {
                                    sigman[k] = {sigman_arr[k][0], sigman_arr[k][1], sigman_arr[k][2]};
                                }
                                add_perm(natoms, ntrans, nprm, nper);
                            }
                        }
                    }
                    symm_crossp(p1, p2, v2);
                    vn = std::sqrt(symm_dot(v2.data(), v2.data(), 3));
                    if (vn > delta) {
                        v2 = {v2[0] / vn, v2[1] / vn, v2[2] / vn};
                        double sp = symm_dot(v2.data(), p0.data(), 3);
                        if (std::abs(sp) < delta) {
                            symm_reflect(natoms, nat, coord_3xn, Vector3D(v2[0], v2[1], v2[2]), Vector3D(p0[0], p0[1], p0[2]), delta, nc, ntrans, del);
                            if (nc == natoms) {
                                if (del > delta3) delta3 = del;
                                std::vector<std::array<double, 3>> sigman_arr(nsg + 1);
                                for (int k = 0; k < nsg; ++k) {
                                    sigman_arr[k] = {sigman[k][0], sigman[k][1], sigman[k][2]};
                                }
                                add_SG(nsg, sigman_arr, {v2[0], v2[1], v2[2]}, {p3[0], p3[1], p3[2]}, delta);
                                for (int k = 0; k < nsg; ++k) {
                                    sigman[k] = {sigman_arr[k][0], sigman_arr[k][1], sigman_arr[k][2]};
                                }
                                add_perm(natoms, ntrans, nprm, nper);
                            }
                        }
                    }
                    symm_crossp(v1, v2, v3);
                    vn = std::sqrt(symm_dot(v3.data(), v3.data(), 3));
                    if (vn > delta) {
                        v3 = {v3[0] / vn, v3[1] / vn, v3[2] / vn};
                        double sp = symm_dot(v3.data(), p0.data(), 3);
                        if (std::abs(sp) < delta) {
                            symm_reflect(natoms, nat, coord_3xn, Vector3D(v3[0], v3[1], v3[2]), Vector3D(p0[0], p0[1], p0[2]), delta, nc, ntrans, del);
                            if (nc == natoms) {
                                if (del > delta3) delta3 = del;
                                std::vector<std::array<double, 3>> sigman_arr(nsg + 1);
                                for (int k = 0; k < nsg; ++k) {
                                    sigman_arr[k] = {sigman[k][0], sigman[k][1], sigman[k][2]};
                                }
                                add_SG(nsg, sigman_arr, {v3[0], v3[1], v3[2]}, {p3[0], p3[1], p3[2]}, delta);
                                for (int k = 0; k < nsg; ++k) {
                                    sigman[k] = {sigman_arr[k][0], sigman_arr[k][1], sigman_arr[k][2]};
                                }
                                add_perm(natoms, ntrans, nprm, nper);
                            }
                        }
                    }
                }
            }
        }

        // Output mirror planes
        for (int i = 0; i < nsg; ++i) {
            if (nout >= 1) std::cout << "\n-- Plane #" << i + 1 << "\n";
            v1 = sigman[i];
            if (nout == 2) {
                std::cout << "  n: " << std::fixed << std::setprecision(5)
                          << std::setw(12) << v1[0] << std::setw(12) << v1[1] << std::setw(12) << v1[2] << "\n";
                std::cout << "    Atoms included:\n";
            }
            int m = 0;
            for (int j = 0; j < natoms; ++j) {
                p3 = {coord[0][j], coord[1][j], coord[2][j]};
                double sp = symm_dot(v1.data(), p3.data(), 3);
                if (std::abs(sp) <= delta) {
                    if (nout == 2) {
                        std::cout << "        " << symb[nat[j] - 1] << " (" << j + 1 << ")\n";
                    }
                    m++;
                    if (std::abs(sp) > delta3) delta3 = std::abs(sp);
                }
            }
            symn[0][i + 1] = v1[0];
            symn[1][i + 1] = v1[1];
            symn[2][i + 1] = v1[2];
            nsym[i + 1][0] = 1;
            nsym[i + 1][4] = m;
        }

        // The rest of the implementation would continue here, but for now let's focus on getting the basic structure working
        // This is a large function, so I'll implement it in stages

        // For now, set some basic outputs
        ncr = 0;
        nsr = 0;
    }

    ni = symcen ? 1 : 0;
    norder = 1 + ni + nsg + ncr + nsr;
    np = 1;
    if (nout >= 1) std::cout << "\n-- PLANES OF SYMMETRY --\n";
    for (int i = 0; i < neq; ++i) {
        int meqi = meq[i];
        if (meqi == 1) continue;
        for (int j1 = 0; j1 < meqi - 1; ++j1) {
            int i1 = ieq[i][j1] - 1;
            p1 = {coord[0][i1], coord[1][i1], coord[2][i1]};
            for (int j2 = j1 + 1; j2 < meqi; ++j2) {
                int i2 = ieq[i][j2] - 1;
                p2 = {coord[0][i2], coord[1][i2], coord[2][i2]};
                p0 = {(p1[0] + p2[0]) / 2.0, (p1[1] + p2[1]) / 2.0, (p1[2] + p2[2]) / 2.0};
                v1 = {p2[0] - p0[0], p2[1] - p0[1], p2[2] - p0[2]};
                double vn_local = std::sqrt(symm_dot(v1.data(), v1.data(), 3));
                if (vn_local > delta) {
                    v1[0] /= vn_local;
                    v1[1] /= vn_local;
                    v1[2] /= vn_local;
                    double sp = symm_dot(v1.data(), p0.data(), 3);
                    if (std::abs(sp) < delta) {
                        symm_reflect(natoms, nat, coord_3xn, Vector3D(v1[0], v1[1], v1[2]), Vector3D(p0[0], p0[1], p0[2]), delta, nc, ntrans, del);
                        if (nc == natoms) {
                            if (del > delta3) delta3 = del;
                            std::vector<std::array<double, 3>> sigman_arr(nsg + 1);
                            for (int k = 0; k < nsg; ++k) {
                                sigman_arr[k] = {sigman[k][0], sigman[k][1], sigman[k][2]};
                            }
                            add_SG(nsg, sigman_arr, {v1[0], v1[1], v1[2]}, {p3[0], p3[1], p3[2]}, delta);
                            for (int k = 0; k < nsg; ++k) {
                                sigman[k] = {sigman_arr[k][0], sigman_arr[k][1], sigman_arr[k][2]};
                            }
                            add_perm(natoms, ntrans, nprm, nper);
                        }
                    }
                }
                symm_crossp(p1, p2, v2);
                vn_local = std::sqrt(symm_dot(v2.data(), v2.data(), 3));
                if (vn_local > delta) {
                    v2[0] /= vn_local;
                    v2[1] /= vn_local;
                    v2[2] /= vn_local;
                    double sp = symm_dot(v2.data(), p0.data(), 3);
                    if (std::abs(sp) < delta) {
                        symm_reflect(natoms, nat, coord_3xn, Vector3D(v2[0], v2[1], v2[2]), Vector3D(p0[0], p0[1], p0[2]), delta, nc, ntrans, del);
                        if (nc == natoms) {
                            if (del > delta3) delta3 = del;
                            std::vector<std::array<double, 3>> sigman_arr(nsg + 1);
                            for (int k = 0; k < nsg; ++k) {
                                sigman_arr[k] = {sigman[k][0], sigman[k][1], sigman[k][2]};
                            }
                            add_SG(nsg, sigman_arr, {v2[0], v2[1], v2[2]}, {p3[0], p3[1], p3[2]}, delta);
                            for (int k = 0; k < nsg; ++k) {
                                sigman[k] = {sigman_arr[k][0], sigman_arr[k][1], sigman_arr[k][2]};
                            }
                            add_perm(natoms, ntrans, nprm, nper);
                        }
                    }
                }
                symm_crossp(v1, v2, v3);
                vn_local = std::sqrt(symm_dot(v3.data(), v3.data(), 3));
                if (vn_local > delta) {
                    v3[0] /= vn_local;
                    v3[1] /= vn_local;
                    v3[2] /= vn_local;
                    double sp = symm_dot(v3.data(), p0.data(), 3);
                    if (std::abs(sp) < delta) {
                        symm_reflect(natoms, nat, coord_3xn, Vector3D(v3[0], v3[1], v3[2]), Vector3D(p0[0], p0[1], p0[2]), delta, nc, ntrans, del);
                        if (nc == natoms) {
                            if (del > delta3) delta3 = del;
                            std::vector<std::array<double, 3>> sigman_arr(nsg + 1);
                            for (int k = 0; k < nsg; ++k) {
                                sigman_arr[k] = {sigman[k][0], sigman[k][1], sigman[k][2]};
                            }
                            add_SG(nsg, sigman_arr, {v3[0], v3[1], v3[2]}, {p3[0], p3[1], p3[2]}, delta);
                            for (int k = 0; k < nsg; ++k) {
                                sigman[k] = {sigman_arr[k][0], sigman_arr[k][1], sigman_arr[k][2]};
                            }
                            add_perm(natoms, ntrans, nprm, nper);
                        }
                    }
                }
            }
        }
    }

    // Output mirror planes
    for (int i = 0; i < nsg; ++i) {
        if (nout >= 1) std::cout << "\n-- Plane #" << i + 1 << "\n";
        v1 = sigman[i];
        if (nout == 2) {
            std::cout << "  n: " << std::fixed << std::setprecision(5)
                      << std::setw(12) << v1[0] << std::setw(12) << v1[1] << std::setw(12) << v1[2] << "\n";
            std::cout << "    Atoms included:\n";
        }
        int m = 0;
        for (int j = 0; j < natoms; ++j) {
            p3 = {coord[0][j], coord[1][j], coord[2][j]};
            double sp = symm_dot(v1.data(), p3.data(), 3);
            if (std::abs(sp) <= delta) {
                if (nout == 2) {
                    std::cout << "        " << symb[nat[j] - 1] << " (" << j + 1 << ")\n";
                }
                m++;
                if (std::abs(sp) > delta3) delta3 = std::abs(sp);
            }
        }
        symn[0][i + 1] = v1[0];
        symn[1][i + 1] = v1[1];
        symn[2][i + 1] = v1[2];
        nsym[i + 1][0] = 1;
        nsym[i + 1][4] = m;
    }

    // Proper rotations
    if (nout >= 1) std::cout << "\n-- Distinct PROPER ROTATIONAL AXES --\n";
    for (int i = 0; i < neq; ++i) {
        int meqi = meq[i];
        for (int j = 0; j < meqi; ++j) {
            int i1 = ieq[i][j] - 1;
            std::vector<double> p0 = {coord_3xn[0][i1], coord_3xn[1][i1], coord_3xn[2][i1]};
            double vn = std::sqrt(symm_dot(p0.data(), p0.data(), 3));
            if (vn < delta) continue;
            std::vector<double> v1 = {p0[0]/vn, p0[1]/vn, p0[2]/vn};
            for (int k = 12; k >= 2; --k) {
                double alpha = 2.0 * pi / k;
                double sp = alpha * 180.0 / pi;
                double sina = std::sin(alpha);
                double cosa = std::cos(alpha);
                symm_rotate(natoms, nat, coord_3xn, Vector3D(v1[0], v1[1], v1[2]), sina, cosa, delta, nc, ntrans, del);
                if (nc == natoms) {
                    std::vector<std::array<double, 3>> rotn_arr(nrot + 1);
                    std::vector<double> rota_arr(nrot + 1);
                    for (int kk = 0; kk < nrot; ++kk) {
                        rotn_arr[kk] = {rotn[kk][0], rotn[kk][1], rotn[kk][2]};
                        rota_arr[kk] = rota[kk];
                    }
                    add_Cn(nrot, rotn_arr, rota_arr, {v1[0], v1[1], v1[2]}, {p0[0], p0[1], p0[2]}, sp, delta);
                    for (int kk = 0; kk < nrot; ++kk) {
                        rotn[kk] = {rotn_arr[kk][0], rotn_arr[kk][1], rotn_arr[kk][2]};
                        rota[kk] = rota_arr[kk];
                    }
                    add_perm(natoms, ntrans, nprm, nper);
                    if (del > delta3) delta3 = del;
                }
            }
        }
    }
    int nsgi = nsg + 1;
    ncr = nrot;
    for (int i = 0; i < nrot; ++i) {
        symn[0][nsgi + i] = rotn[i][0];
        symn[1][nsgi + i] = rotn[i][1];
        symn[2][nsgi + i] = rotn[i][2];
        nsym[nsgi + i][0] = 2;
        nsym[nsgi + i][1] = static_cast<int>(360.0 / rota[i]);
        nsym[nsgi + i][2] = 1;
        nsym[nsgi + i][3] = 0;
    }

    // Improper rotations
    if (nout >= 1) std::cout << "\n-- Distinct IMPROPER ROTATIONAL AXES --\n";
    int nsgicn = nsg + ncr;
    int nsr_count = 0;
    for (int i = 0; i < nrot; ++i) {
        std::vector<double> v1 = rotn[i];
        for (int k = 24; k >= 2; --k) {
            double alpha = 2.0 * pi / k;
            double sina = std::sin(alpha);
            double cosa = std::cos(alpha);
            symm_srotate(natoms, nat, coord_3xn, Vector3D(v1[0], v1[1], v1[2]), sina, cosa, delta, nc, ntrans, del);
            if (nc == natoms && k > 2) {
                if (del > delta3) delta3 = del;
                symn[0][nsgicn + nsr_count] = v1[0];
                symn[1][nsgicn + nsr_count] = v1[1];
                symn[2][nsgicn + nsr_count] = v1[2];
                nsym[nsgicn + nsr_count][0] = 3;
                nsym[nsgicn + nsr_count][1] = k;
                nsym[nsgicn + nsr_count][2] = 1;
                nsym[nsgicn + nsr_count][3] = 0;
                nsym[nsgicn + nsr_count][4] = icent > 0 ? 1 : 0;
                add_perm(natoms, ntrans, nprm, nper);
                nsr_count++;
            }
        }
    }
    nsr = nsr_count;

    // Principal axis
    np = 0;
    int base_idx = nsg + 1;
    for (int i = 0; i < ncr; ++i) {
        int idx = base_idx + i;
        if (nsym[idx][2] == 1 && nsym[idx][1] > np) {
            np = nsym[idx][1];
        }
    }
    if (ncr == 0) np = 0; // Ensure np = 0 for Cs/Ci
    for (int i = 0; i < ncr; ++i) {
        int idx = base_idx + i;
        if (nsym[idx][1] == np && nsym[idx][2] == 1) {
            nsym[idx][3] = 1;
        }
    }
    for (int i = 1; i <= nsg; ++i) {
        nsym[i][3] = 0; // Default classification
    }

    // Final counts
    ni = symcen ? 1 : 0;
    norder = 1 + ni + nsg + ncr + nsr;
    if (nout >= 1) {
        std::cout << "\n-- Number of symmetry operations (including E) = " << norder << std::endl;
    }

    // Print symmetry operations
    if (nout < 2) return;

    std::cout << "\n-- SYMMETRY OPERATIONS --\n";

    if (nsym[0][1] == 0) {
        if (nsym[0][2] > 0) {
            std::cout << "               #1: COM    -- with atom " << symb[nat[nsym[0][2]-1]] << " (#" << nsym[0][2] << ")" << std::endl;
        } else {
            std::cout << "               #1: COM" << std::endl;
        }
    } else {
        if (nsym[0][2] > 0) {
            std::cout << "               #1: INVERSION CENTER  -- with atom " << symb[nat[nsym[0][2]-1]] << " (#" << nsym[0][2] << ")" << std::endl;
        } else {
            std::cout << "               #1: INVERSION CENTER" << std::endl;
        }
    }

    for (int k = 1; k <= nsg; ++k) {
        std::string plane_type = nsym[k][3] == 0 ? "SG " :
                                (nsym[k][3] == 1 ? "SGH" :
                                (nsym[k][3] == 2 ? "SGV" : "SGD"));
        if (nsym[k][4] == 0) {
            std::cout << "               #" << k+1 << ": " << plane_type << std::endl;
        } else {
            std::cout << "               #" << k+1 << ": " << plane_type << "     -- with " << nsym[k][4] << " unmoved atoms" << std::endl;
        }
    }

    int improper_base = base_idx + ncr;
    for (int k = 0; k < nsr; ++k) {
        int idx = improper_base + k;
        if (nsym[idx][2] > 1) {
            std::cout << "               #" << idx+1 << ": S(" << nsym[idx][1] << "^" << nsym[idx][2] << ")" << std::endl;
        } else {
            if (nsym[idx][4] > 0) {
                std::cout << "               #" << idx+1 << ": S(" << nsym[idx][1] << ")   -- with " << nsym[idx][4] << " unmoved atoms" << std::endl;
            } else {
                std::cout << "               #" << idx+1 << ": S(" << nsym[idx][1] << ")" << std::endl;
            }
        }
    }

    // Compute equivalence classes
    symclass(natoms, nprm, nper, nat, symb, nout, nseq, nccl, nscl);
}

void SymmetryDetector::sym_elements(int natoms, const std::vector<int>& nat,
                                    const std::vector<std::vector<double>>& coord,
                                    double delta, int& ng, int& ni, int& nsg, int& ncr,
                                    int& nsr, int& np, std::vector<std::array<double, 3>>& symn,
                                    std::vector<std::vector<int>>& nsym, int& nout, int& nprm,
                                    std::vector<std::vector<int>>& nper, int& nseq,
                                     std::vector<int>& nccl, std::vector<std::vector<int>>& nscl) {
    // Create symb vector with correct element symbols based on atomic numbers
    std::vector<std::string> symb(std::max(90, natoms), "H"); // Initialize with H
    for (int i = 0; i < natoms; ++i) {
        if (nat[i] > 0 && nat[i] <= 90) {
            symb[i] = element_symbols_[nat[i] - 1];
        }
    }
    const int nmax = 150; // Match the constant from the first implementation
    std::vector<std::vector<double>> symn_vec(3, std::vector<double>(nmax, 0.0)); // For first overload
    int norder;
    // Call the first overload
    sym_elements(natoms, nat, coord, symb, delta, nout, norder, ni, nsg, ncr, nsr, np, symn_vec, nsym, nprm, nper, nseq, nccl, nscl);
    // Convert symn_vec to symn
    symn.clear();
    for (const auto& vec : symn_vec) {
        if (vec.size() >= 3) {
            symn.push_back({vec[0], vec[1], vec[2]});
        }
    }
    ng = norder;
}
